import React from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';

type AreaFilters = {
  areaSelected: string;
  subAreaSelected: string;
  merchantSelected: string;
};

interface DepositFiltersProps {
  filters: AreaFilters;
  onFiltersChange: (filters: Partial<AreaFilters>) => void;
  onSearch: () => void;
  filteredAreas: any[];
  filteredSubAreas: any[];
  filteredMerchants: any[];
}

export const DepositFilters: React.FC<DepositFiltersProps> = ({
  filters,
  onFiltersChange,
  onSearch,
  filteredAreas,
  filteredSubAreas,
  filteredMerchants
}) => {
  return (
    <div className="md:px-6 xl:py-8 text-[#6F6F6E] text-[20px] border-b border-[#6F6F6E]">
      <div className="grid grid-cols-1 lg:grid-cols-2 2xl:grid-cols-10 gap-8 py-8">
        <div className="col-span-1 lg:col-span-1 2xl:col-span-3 flex items-center gap-12">
          <Label htmlFor="area">
            エリア：
          </Label>
          <Select
            value={filters.areaSelected}
            onValueChange={(value) => onFiltersChange({ areaSelected: value })}
          >
            <SelectTrigger id="area" className="w-80 text-lg">
              <SelectValue placeholder="全て" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all" className='text-lg'>全て</SelectItem>
              {filteredAreas?.map((item, index) => (
                <SelectItem key={index} value={item.agx_areaid} className='text-lg'>
                  {item.agxAreaName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="col-span-1 lg:col-span-1 2xl:col-span-3 flex items-center gap-2">
          <Label htmlFor="sub-area">
            サブエリア：
          </Label>
          <Select
            value={filters.subAreaSelected}
            onValueChange={(value) => onFiltersChange({ subAreaSelected: value })}
          >
            <SelectTrigger id="sub-area" className="w-80 text-lg">
              <SelectValue placeholder="全て" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all" className='text-lg'>全て</SelectItem>
              {filteredSubAreas?.map((item, index) => (
                <SelectItem key={index} value={item.agxSubAreaid} className='text-lg'>
                  {item.agxSubAreaName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="col-span-1 lg:col-span-2 2xl:col-span-3 flex items-center gap-12">
          <Label htmlFor="merchant">
            加盟店：
          </Label>
          <Select
            value={filters.merchantSelected}
            onValueChange={(value) => onFiltersChange({ merchantSelected: value })}
          >
            <SelectTrigger id="merchant" className="w-80 text-lg">
              <SelectValue placeholder="全て" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all" className='text-lg'>全て</SelectItem>
              {filteredMerchants?.map((item, index) => (
                <SelectItem key={index} value={item.agxMerchantNo} className='text-lg'>
                  {item.agxStoreName}-{item.agxMerchantNo}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="col-span-1 lg:col-span-2 2xl:col-span-1 flex items-center justify-center mt-4 xl:mt-0">
          <Button
            onClick={onSearch}
            className="bg-[#1D9987] hover:bg-[#1D9987]/80 text-white px-8 py-2 text-[20px]"
          >
            検索
          </Button>
        </div>
      </div>
    </div>
  );
};
